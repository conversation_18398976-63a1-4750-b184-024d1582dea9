{% extends "base.html" %}

{% block title %}MCQ Translator{% endblock %}

{% block content %}

<style>
    select[name='sourceLanguage'],
    select[name='destinationLanguage']{
        padding: 0.6rem !important;
        background: #f4f4f4;
        border-radius: 6px !important;
        border: 1px solid #ccc !important;
        font-size: 1rem !important;
        margin-bottom: 1.2rem !important;
    }
</style>
<div class="test-container">
    <div class="test-card">
        <h1>MCQ Translator</h1>
        <p class="test-description">Translate MCQs from one language to another</p>

        <form id="translatorForm" class="login-form" enctype="multipart/form-data">
            <label for="pdfFile">Upload PDF File</label>
            <input type="file" id="pdfFile" name="pdfFile" accept=".pdf" required>

            <label for="totalQuestions">Total Questions</label>
            <input type="number" id="totalQuestions" name="totalQuestions" min="1" value="10" required>

            <label for="explanationStartPage">Explanation Start Page</label>
            <input type="number" id="explanationStartPage" name="explanationStartPage">

            <label for="sourceLanguage">Source Language</label>
            <select id="sourceLanguage" name="sourceLanguage" required>
                <option value="English">English</option>
                <option value="Hindi">Hindi</option>
                <option value="Tamil">Tamil</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <label for="destinationLanguage">Destination Language</label>
            <select id="destinationLanguage" name="destinationLanguage" required>
                <option value="Hindi">Hindi</option>
                <option value="English">English</option>
                <option value="Tamil">Tamil</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <button type="submit">Translate MCQ</button>
        </form>
        <div id="spinner" class="spinner"></div>

        <div id="logContainer" style="display: none; margin-top: 20px;">
            <h3>Progress Log:</h3>
            <div id="progressLog" class="progress-log"></div>
        </div>

        <div id="resultContainer" style="display: none; margin-top: 20px;">
            <h3>Translation Results:</h3>
            <div id="translationResults" class="translation-results"></div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('translatorForm');
    const spinner = document.getElementById('spinner');
    const logContainer = document.getElementById('logContainer');
    const progressLog = document.getElementById('progressLog');
    const resultContainer = document.getElementById('resultContainer');
    const translationResults = document.getElementById('translationResults');

    function addLogEntry(message) {
        const entry = document.createElement('div');
        entry.className = 'log-entry';
        entry.textContent = message;
        progressLog.appendChild(entry);
        progressLog.scrollTop = progressLog.scrollHeight;
    }

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Clear previous results
        progressLog.innerHTML = '';
        translationResults.innerHTML = '';
        resultContainer.style.display = 'none';

        // Show spinner and log container
        spinner.style.display = 'block';
        logContainer.style.display = 'block';

        // Get form values
        const pdfFile = document.getElementById('pdfFile').files[0];
        const totalQuestions = document.getElementById('totalQuestions').value;
        const explanationStartPage = document.getElementById('explanationStartPage').value;
        const sourceLanguage = document.getElementById('sourceLanguage').value;
        const destinationLanguage = document.getElementById('destinationLanguage').value;

        // Validate inputs
        if (!pdfFile || !totalQuestions || !explanationStartPage || !sourceLanguage || !destinationLanguage) {
            addLogEntry('Error: All fields are required');
            spinner.style.display = 'none';
            return;
        }

        if (sourceLanguage === destinationLanguage) {
            addLogEntry('Error: Source and destination languages must be different');
            spinner.style.display = 'none';
            return;
        }

        addLogEntry(`Starting translation for file: ${pdfFile.name}`);
        addLogEntry(`Source language: ${sourceLanguage}, Destination language: ${destinationLanguage}`);
        addLogEntry(`Total questions: ${totalQuestions}, Explanation start page: ${explanationStartPage}`);

        try {
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('pdfFile', pdfFile);
            formData.append('total_questions', totalQuestions);
            formData.append('explanation_start_page', explanationStartPage);
            formData.append('source_language', sourceLanguage);
            formData.append('destination_language', destinationLanguage);
            formData.append('username', 'web_user');

            // Call the API to start translation
            const response = await fetch('/api/mcq-translator-file', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'success') {
                addLogEntry('Translation completed successfully!');
                addLogEntry(`Translation ID: ${data.translation_id}`);
                addLogEntry(`Original S3 Path: ${data.original_s3_path}`);
                if (data.translated_s3_path) {
                    addLogEntry(`Translated S3 Path: ${data.translated_s3_path}`);
                }

                // Show the translation results
                resultContainer.style.display = 'block';

                // Create buttons container
                const buttonsContainer = document.createElement('div');
                buttonsContainer.className = 'buttons-container';

                // Create a button to view the translated content
                const viewButton = document.createElement('button');
                viewButton.textContent = 'View Translated Content';
                viewButton.className = 'view-button';
                viewButton.addEventListener('click', async function() {
                    try {
                        addLogEntry('Fetching translated content...');

                        const contentResponse = await fetch(`/api/get-translated-mcq-content/${data.translation_id}`);
                        const contentData = await contentResponse.json();

                        if (contentData.status === 'success') {
                            // Clear previous content
                            translationResults.innerHTML = '';
                            translationResults.appendChild(buttonsContainer);

                            // Add the translated text in a pre element
                            const textPre = document.createElement('pre');
                            textPre.className = 'translation-text';
                            textPre.textContent = contentData.content;
                            translationResults.appendChild(textPre);

                            addLogEntry('Translated content loaded successfully');
                        } else {
                            addLogEntry(`Error fetching translated content: ${contentData.message}`);
                        }
                    } catch (error) {
                        addLogEntry(`Error: ${error.message}`);
                    }
                });

                // Create download button
                const downloadButton = document.createElement('button');
                downloadButton.textContent = 'Download Translated File';
                downloadButton.className = 'download-button';
                downloadButton.addEventListener('click', function() {
                    const downloadUrl = `/api/download-translated-mcq/${data.translation_id}`;
                    window.open(downloadUrl, '_blank');
                    addLogEntry('Download started...');
                });

                // Create link display and copy functionality
                const linkContainer = document.createElement('div');
                linkContainer.className = 'link-container';

                const linkLabel = document.createElement('label');
                linkLabel.textContent = 'Download Link:';

                const linkInput = document.createElement('input');
                linkInput.type = 'text';
                linkInput.className = 'link-input';
                linkInput.value = `${window.location.origin}/api/download-translated-mcq/${data.translation_id}`;
                linkInput.readOnly = true;

                const copyButton = document.createElement('button');
                copyButton.textContent = 'Copy Link';
                copyButton.className = 'copy-button';
                copyButton.addEventListener('click', function() {
                    linkInput.select();
                    document.execCommand('copy');
                    copyButton.textContent = 'Copied!';
                    setTimeout(() => {
                        copyButton.textContent = 'Copy Link';
                    }, 2000);
                });

                // Add elements to containers
                buttonsContainer.appendChild(viewButton);
                buttonsContainer.appendChild(downloadButton);

                linkContainer.appendChild(linkLabel);
                linkContainer.appendChild(linkInput);
                linkContainer.appendChild(copyButton);

                translationResults.appendChild(buttonsContainer);
                translationResults.appendChild(linkContainer);

            } else {
                addLogEntry(`Error: ${data.message}`);
            }

        } catch (error) {
            addLogEntry(`Error: ${error.message}`);
        } finally {
            spinner.style.display = 'none';
        }
    });
});
</script>

<style>
.translation-results {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.metadata {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.translation-text {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    max-height: 500px;
    overflow-y: auto;
}

.buttons-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.view-button, .download-button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.view-button:hover, .download-button:hover {
    background-color: #45a049;
}

.download-button {
    background-color: #2196F3;
}

.download-button:hover {
    background-color: #1976D2;
}

.link-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.link-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.link-input {
    width: calc(100% - 100px);
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
    font-family: monospace;
    font-size: 14px;
}

.copy-button {
    background-color: #FF9800;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.copy-button:hover {
    background-color: #F57C00;
}
</style>
{% endblock %}